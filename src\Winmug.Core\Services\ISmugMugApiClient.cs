using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Client for interacting with the SmugMug API
/// </summary>
public interface ISmugMugApiClient
{
    /// <summary>
    /// Gets the authenticated user information
    /// </summary>
    Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifies that we have the correct access level for private data
    /// </summary>
    Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets detailed information about the user's access level and permissions
    /// </summary>
    Task<AccessLevelInfo> GetAccessLevelInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the user's root node
    /// </summary>
    Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific node by its ID
    /// </summary>
    Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the child nodes of a specific node
    /// </summary>
    Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all child nodes recursively for a given node
    /// </summary>
    Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets album information for a specific album
    /// </summary>
    Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all images in a specific album
    /// </summary>
    Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the size details for a specific image
    /// </summary>
    Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads image data from the specified URL
    /// </summary>
    Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting
    /// </summary>
    Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the complete folder structure with album counts and size estimates
    /// </summary>
    Task<FolderNode> GetFolderStructureAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents download progress information
/// </summary>
public class DownloadProgress
{
    public long BytesDownloaded { get; set; }
    public long? TotalBytes { get; set; }
    public double? ProgressPercentage => TotalBytes.HasValue && TotalBytes > 0 
        ? (double)BytesDownloaded / TotalBytes.Value * 100 
        : null;
    public TimeSpan Elapsed { get; set; }
    public double? BytesPerSecond { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}
