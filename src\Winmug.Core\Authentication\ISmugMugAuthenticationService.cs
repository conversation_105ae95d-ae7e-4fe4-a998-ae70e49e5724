namespace Winmug.Core.Authentication;

/// <summary>
/// Service for handling SmugMug OAuth 1.0a authentication
/// </summary>
public interface ISmugMugAuthenticationService
{
    /// <summary>
    /// Gets the current OAuth credentials
    /// </summary>
    OAuthCredentials Credentials { get; }

    /// <summary>
    /// Indicates whether the user is currently authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Event raised when authentication status changes
    /// </summary>
    event EventHandler<AuthenticationStatusChangedEventArgs>? AuthenticationStatusChanged;

    /// <summary>
    /// Initiates the OAuth authentication flow by requesting a request token
    /// </summary>
    /// <returns>Request token response containing authorization URL</returns>
    Task<RequestTokenResponse> InitiateAuthenticationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the OAuth authentication flow by exchanging verification code for access token
    /// </summary>
    /// <param name="verificationCode">The verification code entered by the user</param>
    /// <param name="requestToken">The request token from the initial authentication step</param>
    /// <param name="requestTokenSecret">The request token secret from the initial authentication step</param>
    /// <returns>Access token response</returns>
    Task<AccessTokenResponse> CompleteAuthenticationAsync(
        string verificationCode,
        string requestToken,
        string requestTokenSecret,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads stored authentication credentials
    /// </summary>
    /// <returns>True if credentials were loaded successfully</returns>
    Task<bool> LoadStoredCredentialsAsync();

    /// <summary>
    /// Stores authentication credentials securely
    /// </summary>
    Task StoreCredentialsAsync();

    /// <summary>
    /// Clears stored authentication credentials
    /// </summary>
    Task ClearStoredCredentialsAsync();

    /// <summary>
    /// Tests the current credentials by making a simple API call
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if credentials are valid, false otherwise</returns>
    Task<bool> TestCredentialsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates an authenticated HTTP request with OAuth signature
    /// </summary>
    /// <param name="httpMethod">HTTP method (GET, POST, etc.)</param>
    /// <param name="url">Request URL</param>
    /// <param name="parameters">Additional parameters to include in signature</param>
    /// <returns>HttpRequestMessage with OAuth authorization header</returns>
    HttpRequestMessage CreateAuthenticatedRequest(
        HttpMethod httpMethod,
        string url,
        Dictionary<string, string>? parameters = null);
}

/// <summary>
/// Event arguments for authentication status changes
/// </summary>
public class AuthenticationStatusChangedEventArgs : EventArgs
{
    public bool IsAuthenticated { get; }
    public string? UserNickname { get; }
    public Exception? Error { get; }

    public AuthenticationStatusChangedEventArgs(bool isAuthenticated, string? userNickname = null, Exception? error = null)
    {
        IsAuthenticated = isAuthenticated;
        UserNickname = userNickname;
        Error = error;
    }
}
