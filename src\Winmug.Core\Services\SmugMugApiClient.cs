using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;
using Winmug.Core.Authentication;
using Winmug.Core.Models;
using System.Collections.ObjectModel;

namespace Winmug.Core.Services;

/// <summary>
/// Implementation of SmugMug API client
/// </summary>
public class SmugMugApiClient : ISmugMugApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ILogger<SmugMugApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";

    public SmugMugApiClient(
        HttpClient httpClient,
        ISmugMugAuthenticationService authService,
        ILogger<SmugMugApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting authenticated user from SmugMug API");

        var url = $"{BaseApiUrl}!authuser";
        _logger.LogDebug("Making request to: {Url}", url);

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            _logger.LogError("Failed to get authenticated user - response was null");
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        _logger.LogInformation("Successfully retrieved authenticated user: {UserName} ({NickName})",
            response.Response.Name, response.Response.NickName);

        // Enhanced debugging for user object structure
        _logger.LogDebug("User object details:");
        _logger.LogDebug("  Name: {Name}", response.Response.Name);
        _logger.LogDebug("  NickName: {NickName}", response.Response.NickName);
        _logger.LogDebug("  WebUri: {WebUri}", response.Response.WebUri);
        _logger.LogDebug("  Uri: {Uri}", response.Response.Uri);
        _logger.LogDebug("  Uris object: {HasUris}", response.Response.Uris != null ? "Present" : "NULL");

        if (response.Response.Uris != null)
        {
            _logger.LogDebug("  Uris.Node: {HasNode}", response.Response.Uris.Node != null ? "Present" : "NULL");
            _logger.LogDebug("  Uris.UserProfile: {HasUserProfile}", response.Response.Uris.UserProfile != null ? "Present" : "NULL");
            _logger.LogDebug("  Uris.Features: {HasFeatures}", response.Response.Uris.Features != null ? "Present" : "NULL");

            if (response.Response.Uris.Node != null)
            {
                _logger.LogDebug("  Node URI: {NodeUri}", response.Response.Uris.Node.Uri);
                _logger.LogDebug("  Node Locator: {NodeLocator}", response.Response.Uris.Node.Locator ?? "NULL");
                _logger.LogDebug("  Node Description: {NodeDescription}", response.Response.Uris.Node.UriDescription ?? "NULL");
            }
        }

        return response.Response;
    }

    public async Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Verifying private access level");

            // Try to access the authenticated user endpoint which should return private data
            var url = $"{BaseApiUrl}!authuser";
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<dynamic>>(HttpMethod.Get, url, cancellationToken);

            if (response?.Response == null)
            {
                _logger.LogWarning("Failed to get authenticated user response");
                return false;
            }

            // Check if we have private access by looking for ResponseLevel
            var responseJson = JsonSerializer.Serialize(response.Response);
            var responseObj = JsonSerializer.Deserialize<JsonElement>(responseJson);

            _logger.LogDebug("Full authuser response for access verification: {Response}", (object)responseJson);

            if (responseObj.TryGetProperty("User", out JsonElement userElement))
            {
                if (userElement.TryGetProperty("ResponseLevel", out JsonElement responseLevelElement))
                {
                    var responseLevel = responseLevelElement.GetString();
                    _logger.LogInformation("Access level detected: {ResponseLevel}", responseLevel);

                    // We should have "Full" access for private data, not just "Public"
                    var hasFullAccess = !string.Equals(responseLevel, "Public", StringComparison.OrdinalIgnoreCase);

                    if (!hasFullAccess)
                    {
                        _logger.LogWarning("LIMITED ACCESS DETECTED: Only '{ResponseLevel}' access available", responseLevel);
                        _logger.LogWarning("This means you may not be able to access private albums and folders");
                        _logger.LogWarning("To get full access:");
                        _logger.LogWarning("1. Make sure you clicked 'Authorize' during OAuth");
                        _logger.LogWarning("2. Check that your SmugMug account has the necessary permissions");
                        _logger.LogWarning("3. Try re-authenticating and ensure you grant full access");
                    }

                    return hasFullAccess;
                }
            }

            _logger.LogWarning("Could not determine response level from authenticated user response");
            _logger.LogDebug("Response structure: {Response}", (object)responseJson);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify private access");
            return false;
        }
    }

    public async Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user root node");

        var user = await GetAuthenticatedUserAsync(cancellationToken);

        // Log detailed user information for debugging
        _logger.LogDebug("User details for root node lookup:");
        _logger.LogDebug("  Name: '{Name}'", user.Name ?? "NULL");
        _logger.LogDebug("  NickName: '{NickName}'", user.NickName ?? "NULL");
        _logger.LogDebug("  Uri: '{Uri}'", user.Uri ?? "NULL");
        _logger.LogDebug("  WebUri: '{WebUri}'", user.WebUri ?? "NULL");

        if (user.Uris != null)
        {
            _logger.LogDebug("  Uris.Node: {HasNode}", user.Uris.Node != null ? "Present" : "NULL");
            if (user.Uris.Node != null)
            {
                _logger.LogDebug("    Node.Uri: '{NodeUri}'", user.Uris.Node.Uri ?? "NULL");
                _logger.LogDebug("    Node.Locator: '{NodeLocator}'", user.Uris.Node.Locator ?? "NULL");
            }
        }
        else
        {
            _logger.LogDebug("  Uris: NULL");
        }

        // Check if we have limited access first
        var hasPrivateAccess = await VerifyPrivateAccessAsync(cancellationToken);
        if (!hasPrivateAccess)
        {
            _logger.LogError("Cannot access folder structure with limited OAuth permissions");
            _logger.LogError("You have limited access to SmugMug. To access your folder structure:");
            _logger.LogError("1. Re-authenticate with SmugMug");
            _logger.LogError("2. When prompted, make sure to click 'Authorize' or 'Allow'");
            _logger.LogError("3. Grant full access permissions when asked");
            throw new InvalidOperationException("Cannot access folder structure with limited OAuth permissions. Please re-authenticate with full access permissions.");
        }

        // Try multiple approaches to get the root node
        string? rootNodeUri = null;

        // Approach 1: Use the Node URI from the user object (preferred)
        if (user.Uris?.Node?.Uri != null && !string.IsNullOrWhiteSpace(user.Uris.Node.Uri))
        {
            rootNodeUri = user.Uris.Node.Uri.Trim();
            _logger.LogDebug("Using Node URI from user object: '{Uri}'", rootNodeUri);
        }
        // Approach 2: Construct the URI using the user's nickname
        else if (!string.IsNullOrWhiteSpace(user.NickName))
        {
            rootNodeUri = $"{BaseApiUrl}/user/{user.NickName.Trim()}/node";
            _logger.LogWarning("Node URI not available in user object, constructing from nickname: '{Uri}'", rootNodeUri);
        }
        // Approach 3: Try using the user's main URI to get node information
        else if (!string.IsNullOrWhiteSpace(user.Uri))
        {
            // Try to get the user's node by appending /node to their URI
            var userUri = user.Uri.Trim().TrimEnd('/');
            rootNodeUri = $"{userUri}/node";
            _logger.LogWarning("Attempting to construct node URI from user URI: '{Uri}'", rootNodeUri);
        }

        if (string.IsNullOrWhiteSpace(rootNodeUri))
        {
            _logger.LogError("Cannot determine root node URI. User details:");
            _logger.LogError("  Name: '{Name}'", user.Name ?? "NULL");
            _logger.LogError("  NickName: '{NickName}'", user.NickName ?? "NULL");
            _logger.LogError("  Uri: '{Uri}'", user.Uri ?? "NULL");
            _logger.LogError("  Uris: {Uris}", user.Uris != null ? JsonSerializer.Serialize(user.Uris) : "NULL");

            // This suggests the user data is incomplete, likely due to insufficient permissions
            throw new InvalidOperationException("Cannot determine user's root node URI. This indicates insufficient OAuth permissions. Please re-authenticate with full access permissions.");
        }

        // Validate the URI before attempting to use it
        if (!Uri.IsWellFormedUriString(rootNodeUri, UriKind.Absolute))
        {
            _logger.LogError("Constructed root node URI is malformed: '{Uri}'", rootNodeUri);
            _logger.LogError("This indicates a problem with the user data returned from SmugMug");
            throw new InvalidOperationException($"Malformed root node URI: '{rootNodeUri}'. This indicates insufficient OAuth permissions or corrupted user data. Please re-authenticate with full access permissions.");
        }

        _logger.LogDebug("Attempting to get root node from: '{Uri}'", rootNodeUri);

        try
        {
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, rootNodeUri, cancellationToken);

            if (response?.Response == null)
            {
                _logger.LogError("Failed to get user root node - response was null");
                throw new InvalidOperationException("Failed to get user root node");
            }

            _logger.LogInformation("Successfully retrieved root node: {NodeName} (ID: {NodeId}, Type: {Type})",
                response.Response.Name, response.Response.NodeId, response.Response.Type);

            return response.Response;
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("404"))
        {
            _logger.LogError("Root node not found at '{Uri}'. This may indicate insufficient permissions or account issues.", rootNodeUri);
            throw new InvalidOperationException($"Root node not accessible. This may be due to limited OAuth permissions. Please ensure you have 'Full' access permissions.", ex);
        }
        catch (UriFormatException ex)
        {
            _logger.LogError(ex, "URI format exception when accessing root node at '{Uri}'", rootNodeUri);
            throw new InvalidOperationException($"Invalid URI format for root node: '{rootNodeUri}'. This indicates a problem with the user data from SmugMug. Please re-authenticate with full access permissions.", ex);
        }
    }

    /// <summary>
    /// Provides detailed access level information and guidance
    /// </summary>
    public async Task<AccessLevelInfo> GetAccessLevelInfoAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            var hasPrivateAccess = await VerifyPrivateAccessAsync(cancellationToken);
            var hasNodeAccess = user.Uris?.Node?.Uri != null;

            return new AccessLevelInfo
            {
                HasPrivateAccess = hasPrivateAccess,
                HasNodeAccess = hasNodeAccess,
                UserName = user.Name,
                UserNickName = user.NickName,
                AccessLevel = hasPrivateAccess ? "Full" : "Limited",
                CanAccessPrivateContent = hasPrivateAccess && hasNodeAccess,
                Recommendations = GetAccessRecommendations(hasPrivateAccess, hasNodeAccess)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get access level information");
            return new AccessLevelInfo
            {
                HasPrivateAccess = false,
                HasNodeAccess = false,
                AccessLevel = "Unknown",
                CanAccessPrivateContent = false,
                Recommendations = new[] { "Authentication failed. Please try re-authenticating with SmugMug." }
            };
        }
    }

    private static string[] GetAccessRecommendations(bool hasPrivateAccess, bool hasNodeAccess)
    {
        var recommendations = new List<string>();

        if (!hasPrivateAccess)
        {
            recommendations.Add("You have limited access to SmugMug. To access private albums:");
            recommendations.Add("1. Re-authenticate with SmugMug");
            recommendations.Add("2. When prompted, make sure to click 'Authorize' or 'Allow'");
            recommendations.Add("3. Grant full access permissions when asked");
        }

        if (!hasNodeAccess)
        {
            recommendations.Add("Cannot access your folder structure. This may be due to:");
            recommendations.Add("1. Insufficient OAuth permissions");
            recommendations.Add("2. Account configuration issues");
            recommendations.Add("3. SmugMug API limitations for your account type");
        }

        if (hasPrivateAccess && hasNodeAccess)
        {
            recommendations.Add("✓ You have full access to your SmugMug content!");
        }

        return recommendations.ToArray();
    }

    public async Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting node: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get node: {nodeId}");
        }

        return response.Response;
    }

    public async Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting child nodes for: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}!children";
        var nodes = new List<SmugMugNode>();
        
        await foreach (var node in GetPagedResultsAsync<SmugMugNode>(url, cancellationToken))
        {
            nodes.Add(node);
        }

        _logger.LogDebug("Found {Count} child nodes for: {NodeId}", nodes.Count, nodeId);
        return nodes;
    }

    public async Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all child nodes recursively for: {NodeId}", nodeId);
        
        var allNodes = new List<SmugMugNode>();
        var nodesToProcess = new Queue<string>();
        nodesToProcess.Enqueue(nodeId);

        while (nodesToProcess.Count > 0)
        {
            var currentNodeId = nodesToProcess.Dequeue();
            var childNodes = await GetChildNodesAsync(currentNodeId, cancellationToken);
            
            foreach (var childNode in childNodes)
            {
                allNodes.Add(childNode);
                
                // If it's a folder, add it to the queue for recursive processing
                if (childNode.IsFolder)
                {
                    nodesToProcess.Enqueue(childNode.NodeId);
                }
            }
        }

        _logger.LogDebug("Found {Count} total nodes recursively for: {NodeId}", allNodes.Count, nodeId);
        return allNodes;
    }

    public async Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting album: {AlbumKey}", albumKey);
        
        var url = $"{BaseApiUrl}/album/{albumKey}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get album: {albumKey}");
        }

        return response.Response;
    }

    public async Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting images for album: {AlbumKey}", albumKey);
        
        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        var images = new List<SmugMugImage>();
        
        await foreach (var image in GetPagedResultsAsync<SmugMugImage>(url, cancellationToken))
        {
            images.Add(image);
        }

        _logger.LogDebug("Found {Count} images in album: {AlbumKey}", images.Count, albumKey);
        return images;
    }

    public async Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting size details for image: {ImageKey}", imageKey);
        
        var url = $"{BaseApiUrl}/image/{imageKey}!sizedetails";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugImageSizes>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get image size details: {imageKey}");
        }

        return response.Response;
    }

    public async Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        return await DownloadImageAsync(imageUrl, null, cancellationToken);
    }

    public async Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Downloading image from: {ImageUrl}", imageUrl);

        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, imageUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
        response.EnsureSuccessStatusCode();

        var totalBytes = response.Content.Headers.ContentLength;
        var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);

        if (progress == null)
        {
            return responseStream;
        }

        // Wrap the stream with progress reporting
        return new ProgressReportingStream(responseStream, totalBytes, progress);
    }

    private async Task<T?> SendAuthenticatedRequestAsync<T>(HttpMethod method, string url, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating authenticated request for {Method} {Url}", method.Method, url);

        var request = _authService.CreateAuthenticatedRequest(method, url);
        request.Headers.Add("Accept", "application/json");

        _logger.LogDebug("Sending authenticated request to: {Url}", url);
        _logger.LogDebug("Request headers: {Headers}", string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

        var response = await _httpClient.SendAsync(request, cancellationToken);

        _logger.LogDebug("Response status: {StatusCode} {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);

        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogError("API request failed: {StatusCode} {ReasonPhrase} - {Content}",
                response.StatusCode, response.ReasonPhrase, errorContent);
            _logger.LogError("Failed request URL: {Url}", url);
            _logger.LogError("Failed request method: {Method}", method.Method);

            // Log all response headers for debugging
            _logger.LogError("Response headers: {Headers}",
                string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogError("401 Unauthorized - Authentication failed or insufficient permissions");
                _logger.LogError("This usually indicates:");
                _logger.LogError("1. Invalid OAuth signature");
                _logger.LogError("2. Incorrect access token or secret");
                _logger.LogError("3. Malformed authorization header");
                _logger.LogError("4. Clock skew (timestamp too far off)");
                throw new HttpRequestException("Authentication failed. Your session may have expired.", null, response.StatusCode);
            }

            throw new HttpRequestException($"API request failed with status {response.StatusCode}: {response.ReasonPhrase}", null, response.StatusCode);
        }

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        _logger.LogDebug("Response content length: {Length} characters", content.Length);

        // Enhanced debugging for authuser endpoint
        if (url.Contains("!authuser"))
        {
            _logger.LogDebug("Raw authuser response: {Content}", content);
        }

        try
        {
            var result = JsonSerializer.Deserialize<T>(content, _jsonOptions);
            _logger.LogDebug("Successfully deserialized response to {Type}", typeof(T).Name);
            return result;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize response content: {Content}", content.Substring(0, Math.Min(500, content.Length)));
            throw;
        }
    }

    private async IAsyncEnumerable<T> GetPagedResultsAsync<T>(string initialUrl, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var url = initialUrl;

        while (!string.IsNullOrEmpty(url))
        {
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugCollectionResponse<T>>>(HttpMethod.Get, url, cancellationToken);

            if (response?.Response == null)
            {
                yield break;
            }

            foreach (var item in response.Response.Items)
            {
                yield return item;
            }

            // Get next page URL
            url = response.Response.Pages?.NextPage;
        }
    }

    /// <summary>
    /// Get the complete folder structure with album counts and size estimates
    /// </summary>
    public async Task<FolderNode> GetFolderStructureAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching complete folder structure...");

        var rootNode = await GetUserRootNodeAsync(cancellationToken);
        var folderStructure = await BuildFolderStructureRecursiveAsync(rootNode.NodeId, "", cancellationToken);

        _logger.LogInformation("Folder structure fetched successfully");
        return folderStructure;
    }

    /// <summary>
    /// Recursively build the folder structure
    /// </summary>
    private async Task<FolderNode> BuildFolderStructureRecursiveAsync(string nodeId, string parentPath, CancellationToken cancellationToken = default)
    {
        var nodeInfo = await GetNodeAsync(nodeId, cancellationToken);
        var fullPath = string.IsNullOrEmpty(parentPath) ? nodeInfo.Name : $"{parentPath}/{nodeInfo.Name}";

        var folderNode = new FolderNode
        {
            NodeId = nodeId,
            Name = nodeInfo.Name,
            Description = nodeInfo.Description ?? "",
            Type = nodeInfo.Type,
            UrlName = nodeInfo.UrlName ?? "",
            FullPath = fullPath,
            DateCreated = nodeInfo.DateAdded ?? DateTime.MinValue,
            DateModified = nodeInfo.DateModified ?? DateTime.MinValue
        };

        // Get child nodes
        var childNodes = await GetChildNodesAsync(nodeId, cancellationToken);

        foreach (var childNode in childNodes)
        {
            if (childNode.IsAlbum)
            {
                // This is an album - get album details
                var albumInfo = await GetAlbumInfoAsync(childNode, fullPath, cancellationToken);
                folderNode.Albums.Add(albumInfo);
                folderNode.TotalImageCount += albumInfo.ImageCount;
                folderNode.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }
            else if (childNode.IsFolder)
            {
                // This is a subfolder - recurse
                var subFolder = await BuildFolderStructureRecursiveAsync(childNode.NodeId, fullPath, cancellationToken);
                folderNode.Children.Add(subFolder);
                folderNode.TotalImageCount += subFolder.TotalImageCount;
                folderNode.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
            }
        }

        folderNode.HasChildren = folderNode.Children.Count > 0 || folderNode.Albums.Count > 0;

        return folderNode;
    }

    /// <summary>
    /// Get detailed album information including image count and size estimate
    /// </summary>
    private async Task<AlbumInfo> GetAlbumInfoAsync(SmugMugNode albumNode, string parentPath, CancellationToken cancellationToken = default)
    {
        // Extract album key from the Album URI if available
        var albumKey = "";
        if (albumNode.Uris?.Album?.Uri != null)
        {
            var albumUri = albumNode.Uris.Album.Uri;
            albumKey = albumUri.Split('/').LastOrDefault() ?? "";
        }

        var albumInfo = new AlbumInfo
        {
            NodeId = albumNode.NodeId,
            AlbumKey = albumKey,
            Name = albumNode.Name,
            Description = albumNode.Description ?? "",
            UrlName = albumNode.UrlName ?? "",
            FullPath = $"{parentPath}/{albumNode.Name}",
            DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
            DateModified = albumNode.DateModified ?? DateTime.MinValue
        };

        // Get detailed album statistics if we have the album key
        if (!string.IsNullOrEmpty(albumKey))
        {
            try
            {
                var album = await GetAlbumAsync(albumKey, cancellationToken);

                albumInfo.ImageCount = album.ImageCount ?? 0;

                // Estimate size (assume average 3MB per image for estimation)
                albumInfo.EstimatedSizeBytes = albumInfo.ImageCount * 3 * 1024 * 1024;

                albumInfo.Privacy = album.Privacy ?? "";
                albumInfo.IsPublic = albumInfo.Privacy == "Public";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get detailed album info for {AlbumKey}", albumKey);
                // Continue with basic info
            }
        }

        return albumInfo;
    }
}
