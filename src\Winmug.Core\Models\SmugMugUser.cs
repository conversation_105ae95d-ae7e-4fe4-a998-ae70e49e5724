using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug user account
/// </summary>
public class SmugMugUser
{
    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("NickName")]
    public string NickName { get; set; } = string.Empty;

    [JsonPropertyName("ViewPassHint")]
    public string? ViewPassHint { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Uris")]
    public SmugMugUris? Uris { get; set; }
}

/// <summary>
/// Contains URIs for related SmugMug resources
/// </summary>
public class SmugMugUris
{
    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("UserProfile")]
    public SmugMugUriInfo? UserProfile { get; set; }

    [JsonPropertyName("Features")]
    public SmugMugUriInfo? Features { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }
}

/// <summary>
/// Contains URI information for a SmugMug resource
/// </summary>
public class SmugMugUriInfo
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }
}

/// <summary>
/// Represents a SmugMug user profile with additional profile information
/// </summary>
public class SmugMugUserProfile
{
    [JsonPropertyName("BioText")]
    public string? BioText { get; set; }

    [JsonPropertyName("FirstName")]
    public string? FirstName { get; set; }

    [JsonPropertyName("LastName")]
    public string? LastName { get; set; }

    [JsonPropertyName("DisplayName")]
    public string? DisplayName { get; set; }

    [JsonPropertyName("ContactEmail")]
    public string? ContactEmail { get; set; }

    [JsonPropertyName("ValidEmail")]
    public bool? ValidEmail { get; set; }

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Uris")]
    public SmugMugUserProfileUris? Uris { get; set; }
}

/// <summary>
/// Contains URIs for SmugMug user profile related resources
/// </summary>
public class SmugMugUserProfileUris
{
    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("BioImage")]
    public SmugMugUriInfo? BioImage { get; set; }

    [JsonPropertyName("CoverImage")]
    public SmugMugUriInfo? CoverImage { get; set; }
}
